<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>零工市场详情-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/detail.css?v=202507231048" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
    
    <style>
        .detail-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .detail-header {
            border-bottom: 1px solid #e6e6e6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .detail-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .detail-meta {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }

        .meta-icon {
            font-size: 16px;
        }

        .detail-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .content-main {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .content-sidebar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            height: fit-content;
        }

        .info-section {
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            padding: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #0052d9;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .info-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 600;
        }

        .description-text {
            line-height: 1.6;
            color: #555;
            font-size: 14px;
        }

        .contact-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .contact-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .contact-icon {
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e6e6e6;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #0052d9;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background: #545b62;
            transform: translateY(-1px);
        }

        .loading-container {
            text-align: center;
            padding: 60px 20px;
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0052d9;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-container {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .error-icon {
            font-size: 64px;
            color: #e0e0e0;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .detail-content {
                grid-template-columns: 1fr;
            }
            
            .detail-meta {
                flex-direction: column;
                gap: 10px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div id="headerBar"></div>
    
    <!-- 主要内容 -->
    <div class="conAuto2" style="margin-top: 20px; margin-bottom: 40px;">
        <a href="javascript:history.back()" class="back-btn">
            <span>←</span>
            <span>返回列表</span>
        </a>
        
        <!-- 加载状态 -->
        <div id="loadingContainer" class="loading-container">
            <div class="loading-spinner"></div>
            <p style="margin-top: 15px; color: #666;">正在加载零工市场详情...</p>
        </div>
        
        <!-- 错误状态 -->
        <div id="errorContainer" class="error-container" style="display: none;">
            <div class="error-icon">🏢</div>
            <h3 style="color: #666; margin-bottom: 12px;">零工市场信息不存在</h3>
            <p style="color: #999; font-size: 14px;">该零工市场可能已下线或不存在</p>
        </div>
        
        <!-- 详情内容 -->
        <div id="detailContainer" class="detail-container" style="display: none;">
            <div class="detail-header">
                <h1 id="marketName" class="detail-title">零工市场名称</h1>
                <div class="detail-meta">
                    <div class="meta-item">
                        <span class="meta-icon">🏷️</span>
                        <span id="marketType">市场类型</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">📍</span>
                        <span id="regionName">所在区域</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">👁️</span>
                        <span>浏览 <span id="viewCount">0</span> 次</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">⭐</span>
                        <span>评分 <span id="ratingAverage">0</span> 分</span>
                    </div>
                </div>
            </div>
            
            <div class="detail-content">
                <div class="content-main">
                    <!-- 基本信息 -->
                    <div class="info-section">
                        <h2 class="section-title">基本信息</h2>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">服务类别</div>
                                <div id="serviceCategory" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">容纳人数</div>
                                <div id="capacity" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">日均需求</div>
                                <div id="dailyDemandAverage" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">服务费用</div>
                                <div id="serviceFee" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">营业时间</div>
                                <div id="operatingHours" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">状态</div>
                                <div id="status" class="info-value">--</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 详细描述 -->
                    <div class="info-section">
                        <h2 class="section-title">详细描述</h2>
                        <div id="description" class="description-text">
                            暂无详细描述
                        </div>
                    </div>
                    
                    <!-- 地址信息 -->
                    <div class="info-section">
                        <h2 class="section-title">地址信息</h2>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">详细地址</div>
                                <div id="address" class="info-value">--</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">交通信息</div>
                                <div id="transportInfo" class="info-value">--</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="content-sidebar">
                    <!-- 联系信息 -->
                    <div class="contact-info">
                        <div class="contact-title">联系信息</div>
                        <div class="contact-item">
                            <span class="contact-icon">👤</span>
                            <span>联系人：<span id="contactPerson">--</span></span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">📞</span>
                            <span>电话：<span id="contactPhone">--</span></span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-icon">✉️</span>
                            <span>邮箱：<span id="contactEmail">--</span></span>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div id="totalWorkers" class="stat-number">0</div>
                            <div class="stat-label">注册零工</div>
                        </div>
                        <div class="stat-item">
                            <div id="activeJobs" class="stat-number">0</div>
                            <div class="stat-label">活跃岗位</div>
                        </div>
                        <div class="stat-item">
                            <div id="completedJobs" class="stat-number">0</div>
                            <div class="stat-label">完成订单</div>
                        </div>
                        <div class="stat-item">
                            <div id="successRate" class="stat-number">0%</div>
                            <div class="stat-label">成功率</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <div id="footerBar"></div>
    
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    
    <script>
        // 公用模块html
        headerBar();
        footerBar();
        
        // 获取URL参数
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }
        
        // 获取零工市场详情
        function loadMarketDetail(marketId) {
            $.ajax({
                url: '/api/public/labor/markets/' + marketId,
                method: 'GET',
                success: function(response) {
                    if (response.code == 0 || response.code == 200) {
                        var market = response.data;
                        renderMarketDetail(market);
                    } else {
                        showError();
                    }
                },
                error: function() {
                    showError();
                }
            });
        }
        
        // 渲染零工市场详情
        function renderMarketDetail(market) {
            $('#marketName').text(market.marketName || '未知零工市场');
            $('#marketType').text(market.marketType || '--');
            $('#regionName').text(market.regionName || '--');
            $('#viewCount').text(market.viewCount || 0);
            $('#ratingAverage').text(market.ratingAverage || 0);
            
            $('#serviceCategory').text(market.serviceCategory || '--');
            $('#capacity').text(market.capacity ? market.capacity + '人' : '--');
            $('#dailyDemandAverage').text(market.dailyDemandAverage ? market.dailyDemandAverage + '人' : '--');
            
            // 格式化服务费用
            var feeText = '免费';
            if (market.serviceFeeMin && market.serviceFeeMax) {
                feeText = '￥' + market.serviceFeeMin + '-' + market.serviceFeeMax + '/天';
            } else if (market.serviceFeeMin) {
                feeText = '￥' + market.serviceFeeMin + '+/天';
            }
            $('#serviceFee').text(feeText);
            
            $('#operatingHours').text(market.operatingHours || '--');
            $('#status').text(market.status == 'active' ? '营业中' : '暂停营业');
            
            $('#description').text(market.description || '暂无详细描述');
            $('#address').text(market.address || '--');
            $('#transportInfo').text(market.transportInfo || '--');
            
            $('#contactPerson').text(market.contactPerson || '--');
            $('#contactPhone').text(market.contactPhone || '--');
            $('#contactEmail').text(market.contactEmail || '--');
            
            $('#totalWorkers').text(market.totalWorkers || 0);
            $('#activeJobs').text(market.activeJobs || 0);
            $('#completedJobs').text(market.completedJobs || 0);
            $('#successRate').text(market.successRate ? market.successRate + '%' : '0%');
            
            // 显示详情容器
            $('#loadingContainer').hide();
            $('#detailContainer').show();
        }
        
        // 显示错误信息
        function showError() {
            $('#loadingContainer').hide();
            $('#errorContainer').show();
        }
        
        // 页面初始化
        $(document).ready(function() {
            var marketId = getUrlParameter('id');
            if (marketId) {
                loadMarketDetail(marketId);
            } else {
                showError();
            }
        });
    </script>
</body>

</html>
