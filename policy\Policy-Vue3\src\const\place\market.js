// 零工市场管理表格配置
export function createMarketTableOption() {
    return Promise.resolve({
        // 表格列配置
        columns: [
            {
                prop: 'marketId',
                label: '市场ID',
                width: 80,
                align: 'center'
            },
            {
                prop: 'marketName',
                label: '市场名称',
                minWidth: 150,
                align: 'center',
                showOverflowTooltip: true
            },
            {
                prop: 'marketCode',
                label: '市场编码',
                width: 120,
                align: 'center'
            },
            {
                prop: 'marketType',
                label: '市场类型',
                width: 100,
                align: 'center',
                slot: 'marketType'
            },
            {
                prop: 'regionName',
                label: '区域',
                width: 100,
                align: 'center'
            },
            {
                prop: 'contactPerson',
                label: '联系人',
                width: 100,
                align: 'center'
            },
            {
                prop: 'contactPhone',
                label: '联系电话',
                width: 120,
                align: 'center'
            },
            {
                prop: 'workerCapacity',
                label: '零工容纳量',
                width: 100,
                align: 'center'
            },
            {
                prop: 'currentWorkerCount',
                label: '当前零工数',
                width: 100,
                align: 'center'
            },
            {
                prop: 'dailyAvgDemand',
                label: '日均需求',
                width: 100,
                align: 'center'
            },
            {
                prop: 'managementFee',
                label: '管理费用',
                width: 100,
                align: 'center'
            },
            {
                prop: 'isFeatured',
                label: '是否推荐',
                width: 100,
                align: 'center',
                slot: 'isFeatured'
            },
            {
                prop: 'status',
                label: '状态',
                width: 80,
                align: 'center',
                slot: 'status'
            },
            {
                prop: 'viewCount',
                label: '浏览次数',
                width: 100,
                align: 'center'
            },
            {
                prop: 'createTime',
                label: '创建时间',
                width: 180,
                align: 'center',
                formatter: (row) => {
                    return row.createTime ? row.createTime.substring(0, 10) : ''
                }
            }
        ],
        
        // 搜索字段配置
        searchColumns: [
            {
                prop: 'marketName',
                label: '市场名称',
                type: 'input',
                placeholder: '请输入市场名称'
            },
            {
                prop: 'marketType',
                label: '市场类型',
                type: 'select',
                placeholder: '请选择市场类型',
                options: [
                    { label: '综合市场', value: '综合市场' },
                    { label: '专业市场', value: '专业市场' },
                    { label: '临时市场', value: '临时市场' }
                ]
            },
            {
                prop: 'regionCode',
                label: '区域',
                type: 'select',
                placeholder: '请选择区域',
                options: [
                    { label: '市南区', value: '370202' },
                    { label: '市北区', value: '370203' },
                    { label: '崂山区', value: '370212' }
                ]
            },
            {
                prop: 'status',
                label: '状态',
                type: 'select',
                placeholder: '请选择状态',
                options: [
                    { label: '正常', value: '0' },
                    { label: '停用', value: '1' }
                ]
            }
        ],
        
        // 表单字段配置（用于新增/编辑对话框）
        formFields: [
            {
                prop: 'marketName',
                label: '市场名称',
                type: 'input',
                required: true,
                placeholder: '请输入市场名称'
            },
            {
                prop: 'marketCode',
                label: '市场编码',
                type: 'input',
                placeholder: '请输入市场编码'
            },
            {
                prop: 'marketType',
                label: '市场类型',
                type: 'select',
                required: true,
                placeholder: '请选择市场类型',
                options: [
                    { label: '综合市场', value: '综合市场' },
                    { label: '专业市场', value: '专业市场' },
                    { label: '临时市场', value: '临时市场' }
                ]
            },
            {
                prop: 'address',
                label: '市场地址',
                type: 'input',
                required: true,
                placeholder: '请输入市场地址'
            },
            {
                prop: 'regionCode',
                label: '区域代码',
                type: 'input',
                placeholder: '请输入区域代码'
            },
            {
                prop: 'regionName',
                label: '区域名称',
                type: 'input',
                placeholder: '请输入区域名称'
            },
            {
                prop: 'contactPerson',
                label: '联系人',
                type: 'input',
                placeholder: '请输入联系人'
            },
            {
                prop: 'contactPhone',
                label: '联系电话',
                type: 'input',
                placeholder: '请输入联系电话'
            },
            {
                prop: 'contactEmail',
                label: '联系邮箱',
                type: 'input',
                placeholder: '请输入联系邮箱'
            },
            {
                prop: 'operatingHours',
                label: '营业时间',
                type: 'input',
                placeholder: '请输入营业时间'
            },
            {
                prop: 'workerCapacity',
                label: '零工容纳量',
                type: 'number',
                min: 0,
                placeholder: '请输入零工容纳量'
            },
            {
                prop: 'currentWorkerCount',
                label: '当前零工数',
                type: 'number',
                min: 0,
                placeholder: '请输入当前零工数'
            },
            {
                prop: 'dailyAvgDemand',
                label: '日均需求',
                type: 'number',
                min: 0,
                placeholder: '请输入日均需求'
            },
            {
                prop: 'peakDemandTime',
                label: '用工高峰时段',
                type: 'input',
                placeholder: '请输入用工高峰时段'
            },
            {
                prop: 'managementFee',
                label: '管理费用',
                type: 'input',
                placeholder: '元/人/天'
            },
            {
                prop: 'serviceFeeRate',
                label: '服务费率',
                type: 'input',
                placeholder: '%'
            },
            {
                prop: 'safetyMeasures',
                label: '安全措施',
                type: 'textarea',
                placeholder: '请输入安全措施描述'
            },
            {
                prop: 'description',
                label: '市场描述',
                type: 'textarea',
                placeholder: '请输入市场详细描述'
            },
            {
                prop: 'isFeatured',
                label: '是否推荐',
                type: 'radio',
                options: [
                    { label: '是', value: 1 },
                    { label: '否', value: 0 }
                ]
            },
            {
                prop: 'status',
                label: '状态',
                type: 'radio',
                options: [
                    { label: '正常', value: '0' },
                    { label: '停用', value: '1' }
                ]
            },
            {
                prop: 'remark',
                label: '备注',
                type: 'textarea',
                placeholder: '请输入备注'
            }
        ]
    })
}
