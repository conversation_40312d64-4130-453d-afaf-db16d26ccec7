<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>找场地-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/index.css?v=202503281048" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />

    <style>
        /* 加载动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 场地信息卡片样式 */
        .place-card-new {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e6e6e6;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .place-card-new:hover {
            border-color: #0052d9;
            box-shadow: 0 4px 16px rgba(0,82,217,0.15);
            transform: translateY(-2px);
        }

        .place-card-new.featured {
            border-color: #28a745;
            background: linear-gradient(135deg, #fff 0%, #f8fff9 100%);
        }

        .featured-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .card-header-new {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .place-title-new {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
            flex: 1;
            margin-right: 15px;
        }

        .place-rent-new {
            font-size: 16px;
            font-weight: bold;
            color: #ff6000;
            background: #fff5f0;
            padding: 6px 12px;
            border-radius: 6px;
            white-space: nowrap;
        }

        .place-meta-tags-new {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
        }

        .meta-tag-new {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
        }

        .type-tag-new {
            background: #e3f2fd;
            color: #1976d2;
        }

        .level-tag-new {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .region-tag-new {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .info-grid-new {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item-new {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-icon-new {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .info-content-new {
            flex: 1;
        }

        .info-label-new {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .info-value-new {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .card-footer-new {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .place-address-new {
            font-size: 12px;
            color: #999;
            flex: 1;
        }

        .view-count-new {
            font-size: 12px;
            color: #999;
        }

        .detail-btn-new {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s;
            margin-left: 8px;
        }

        .detail-btn-new:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        /* 确保页面底部正确 */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .width100 {
            flex: 1;
        }

        #footerBar {
            margin-top: auto;
        }
    </style>
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- main start -->
    <div class="">
        <!-- banner 开始 -->
        <div class="bannerBox pr none">
            <!-- ko if:bagnnerList().length>0 -->
            <div class="bannerSlide" data-bind="foreach:bagnnerList">
                <img src="./image/index_banner.png" data-bind="attr:{src:fullPath}">
            </div>
            <div class="hd" data-bind="visible:bagnnerList().length>1"><ul></ul></div>
            <!-- /ko -->
            <!-- ko if:bagnnerList().length==0 -->
            <div class="bannerSlide" >
                <img src="./image/index_banner.png" >
            </div>
            <!-- /ko -->
            <div class="conAuto2 pr">
                <!-- slideTxtBox start -->
                <!-- ko if:cdggList().length>0 -->
                <div class="slideTxtBox pa pr">
                    <div class="bd">
                        <ul data-bind="foreach:cdggList()">
                            <li class="pr">
                                <div class="sliLiTop clearfix">
                                    <div class="fl date">
                                        <p class="top" data-bind="text:date"></p>
                                        <p class="bottom" data-bind="text:baseCreateTime"></p>
                                    </div>
                                    <a href="javascript:;"
                                        data-bind="text:baseName,attr:{title:baseName,href:'./placeDetail.html?pageType='+baseId+'&id='+parkId}"
                                        class="block fl title transi paraoverflow2"></a>
                                </div>
                                <div class="mainText paraoverflow3" data-bind="html:parkNoticeDetail"></div>
                                <a href="javascript:;" class="pa animationBtn" style="left:0px;bottom:0px" target="_blank"
                                    data-bind="attr:{href:'./placeDetail.html?pageType='+baseId+'&id='+parkId}">
                                    <img src="./image/index_more.png">
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div data-bind="foreach:cdggList()">
                        <div class="hd clearfix pa">
                            <div class="fr">
                                <a href="javascript:;" class="prev fl transi"></a>
                                <p class="pageState fl mt2"></p>
                                <a href="javascript:;" class="next fr transi"></a>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /ko -->
                <!-- slideTxtBox end -->

            </div>
        </div>
        <!-- banner end -->

        <!-- bannerNew  -->
         <div class="bannerNew"></div>
        <!-- bannerNew end -->

        <!-- 第一部分 start -->
        <div class="conAuto2 cycdBox">
            <ul class="clearfix numout">
                <li class="fl cp bg1" onclick="linkPage(1)">
                    <div id="onerun01" class="num"></div>
                    <p class="text">场地数量（个）</p>
                </li>
                <li class="fl  bg2">
                    <div id="onerun02" class="num"></div>
                    <p class="text">场地面积（㎡）</p>
                </li>
                <li class="fl  bg3">
                    <div id="onerun03" class="num"></div>
                    <p class="text">可使用面积（㎡）</p>
                </li>
                <li class="fl  bg4">
                    <div id="onerun04" class="num"></div>
                    <p class="text">已入驻企业（家）</p>
                </li>
            </ul>
        </div>
        <!-- 第三部分 start -->
        <div class="cycdMainBox">
            <div class="conAuto2">
                <div class="clearfix mb20">
                    <div class="fl">
                        <p class="contentTitle">创业<em>场地</em></p>
                        <p class="xg"></p>
                    </div>
                    <a href="javascript:;" onclick="moreList()"  class="moreBtn block fr transi mt20">
                        <span class="inlineblock text-white">更多</span>
                    </a>
                </div>
                <!-- 下拉筛选 -->
                <div class="selectBox clearfix">
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module1">
                            <div class="title ">场地区域</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:positionName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:positionList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('0',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module2">
                            <div class="title ">场地类型</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:typeName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:typeList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('1',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module3">
                            <div class="title ">场地面积</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:areaName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:areaList01()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('2',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module4">
                            <div class="title ">场地等级</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:levelName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:levelList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('3',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module5">
                            <div class="title ">行业方向</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:directionName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:directionList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('4',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="fr pr">
                        <div class="selectModule clearfix">
                            <div class="title ">运营模式</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:moneyName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none" style="right: 0;">
                            <i class="arrowIcon" style="left: 60%;"></i>
                            <ul data-bind="foreach:moneyList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('5',event)}"></li>
                            </ul>
                        </div>
                    
                    </div>
                </div>
                <!-- 下拉筛选 -->
                
                <!-- 场地信息列表 -->
                <div class="placeInfoList" id="placeInfoList">
                    <!-- 加载状态 -->
                    <div id="loadingState" style="display: none; text-align: center; padding: 40px;">
                        <div class="loading-spinner" style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #0052d9; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                        <p style="margin-top: 15px; color: #666;">正在加载场地信息...</p>
                    </div>

                    <!-- 场地信息网格 -->
                    <div id="placeGrid" style="display: none;">
                        <!-- 场地信息卡片将在这里动态生成 -->
                    </div>

                    <!-- 无数据提示 -->
                    <div id="noDataTip" style="display: none; text-align: center; padding: 80px 20px; background: rgba(255,255,255,0.95); border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); margin: 40px auto; max-width: 400px;">
                        <div style="font-size: 64px; color: #e0e0e0; margin-bottom: 24px; line-height: 1;">🏢</div>
                        <h3 style="color: #666; margin-bottom: 12px; font-size: 20px; font-weight: 500;">暂无场地信息</h3>
                        <p style="color: #999; font-size: 14px; line-height: 1.5; margin: 0;">请调整筛选条件或稍后再试</p>
                    </div>

                    <!-- 原有的knockout绑定列表（保持兼容） -->
                    <ul class="clearfix cycdList" data-bind="foreach:cycdList()">
                        <li class="fl transi pr">
                            <!-- <em class="tabs pa">高能级园区</em> -->
                            <a class=" block" href="javascript:;" target="_blank"
                            data-bind="attr:{href:'./placeDetail.html?id='+baseId}">
                            <p  class="title block textoverflow"
                                data-bind="text:parkName,attr:{title:parkName}"></p>
                                <div class="clearfix">
                                    <div class="fl mainLeft">

                                        <div class="clearfix">
                                            <p class="fl textoverflow bq1" data-bind="text:parkType,visible:parkType"></p>
                                            <p class="fl textoverflow bq2" data-bind="text:parkLevel,visible:parkLevel"></p>
                                        </div>
                                        <p class="liText textoverflow mt10">可使用面积：<span data-bind="text:acreage+'平方米'"></p>
                                        <p class="liText textoverflow">已入驻企业：<span data-bind="text:rzCompanyCount+'家'"></span>
                                        </p>
                                        <p class="liText textoverflow">招商时间：
                                           <!-- ko if:isOpenSettle=='0' -->
                                           <span data-bind="text:applyTimeStatus==0?'长期':((applyStartDate?applyStartDate.substring(0,10):'--')+'至'+(applyEndDate?applyEndDate.substring(0,10):'--'))"></span>
                                           <!-- /ko -->
                                           <!-- ko if:isOpenSettle=='1' -->
                                           定期招商，暂未开放
                                           <!-- /ko -->
                                        </p>

                                    </div>
                                    <p  class="fr block imgA transi">
                                        <!-- ko if:imageUrl -->
                                        <img src="" class="mainImg transi" data-bind="attr:{src:imageUrl}">
                                        <!-- /ko -->
                                        <!-- ko if:!imageUrl -->
                                        <img src="../public/images/pics/pic_noList.png" class="mainImg transi">
                                        <!-- /ko -->
                                    </p>
                                </div>

                                <p class="pos textoverflow" data-bind="text:address,attr:{title:address}">
                                </p>
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- 暂无数据 -->
                <div class="nodataPic none nodataPicCycd"></div>
            </div>
        </div>
        <!-- 第三部分 end -->
        <!-- 第四部分 start -->
        <!-- <div class="rzlcBox">
            <div class="conAuto2">
                <div>
                    <p class="contentTitle">场地服务商<em>认证流程</em></p>
                    <p class="xg"></p>
                </div>
                <div class="mt50">
                    <div class="clearfix ulDiv">
                        <div class="clearfix fl liDiv transi">
                            <img src="./image/index4LiImg1.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">1</p>
                                <p class="f18 text-gray3 mt10 ml10">场地服务商登录</p>
                            </div>
                        </div>
                        <img src="./image/index4Jt.png" class="fl index4JtImg">
                        <div class="clearfix fl liDiv transi">
                            <img src="./image/index4LiImg2.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">2</p>
                                <p class="f18 text-gray3 mt10 ml10">在线实名认证</p>
                            </div>
                        </div>
                        <img src="./image/index4Jt.png" class="fl index4JtImg">
                        <div class="clearfix fl liDiv transi">
                            <img src="./image/index4LiImg3.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">3</p>
                                <p class="f18 text-gray3 mt10 ml10">审核</p>
                            </div>
                        </div>
                        <img src="./image/index4Jt.png" class="fl index4JtImg">
                        <div class="clearfix fl liDiv transi mr0">
                            <img src="./image/index4LiImg4.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">4</p>
                                <p class="f18 text-gray3 mt10 ml10">认证成功</p>
                            </div>
                        </div>
                        <a href="javascript:;" class="fl block ml20 animationBtn" onclick="sqrzFun()">
                            <img src="./image/index4Ljrz.png">
                        </a>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
    <!-- main end -->
    <!-- 底部 开始 -->
    <div id="footerBar"> </div>
    <!-- 底部 开始 -->
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--jquery js-->
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript" charset="utf-8">
    </script>
    <script src="../public/numberRun/numberRunAll.js" type="text/javascript" charset="utf-8"></script>
    <!--分页 js-->
    <script type="text/javascript" src="../public/plugins/pagination/jquery.pagination.js"></script>
    <!--common js-->
    <!-- <script src="../public/js/api.js"></script> -->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/index.js?v=202503261407" type="text/javascript" charset="utf-8"></script>
</body>

</html>