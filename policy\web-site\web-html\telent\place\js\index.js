// 公用模块html
headerBar()
footerBar()

$("#placePage").addClass("on")
function clearsx(){
    localStorage.removeItem('cdposition');
    localStorage.removeItem('cdtype');
    localStorage.removeItem('cdarea');
    localStorage.removeItem('cddirection');
    localStorage.removeItem('cdmoney');
    localStorage.removeItem('cdlevel');
}
clearsx()

// API基础配置
const API_BASE_URL = '/api';

// 场地信息API调用函数
const placeAPI = {
    // 获取场地列表
    getPlaceList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/list`,
            method: 'GET',
            data: params
        });
    },

    // 获取推荐场地列表
    getFeaturedPlaceList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/featured`,
            method: 'GET',
            data: params
        });
    },

    // 获取场地统计信息
    getPlaceStatistics: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/statistics`,
            method: 'GET'
        });
    },

    // 获取所有场地类型
    getAllPlaceTypes: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/types`,
            method: 'GET'
        });
    },

    // 获取所有场地等级
    getAllPlaceLevels: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/levels`,
            method: 'GET'
        });
    },

    // 获取所有区域
    getAllRegions: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/regions`,
            method: 'GET'
        });
    },

    // 获取所有行业方向
    getAllIndustryDirections: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/industries`,
            method: 'GET'
        });
    },

    // 获取所有运营模式
    getAllOperationModes: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/operations`,
            method: 'GET'
        });
    },

    // 根据关键词搜索场地
    searchPlaces: function(keyword) {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/search`,
            method: 'GET',
            data: { keyword: keyword }
        });
    },

    // 获取场地详情
    getPlaceDetail: function(placeId) {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/public/${placeId}`,
            method: 'GET'
        });
    }
};

// 用工信息API调用函数
const employmentAPI = {
    // 获取用工信息列表
    getEmploymentList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/place/employment/public/list`,
            method: 'GET',
            data: params
        });
    },

    // 获取推荐用工信息列表
    getFeaturedEmploymentList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/place/employment/public/featured`,
            method: 'GET',
            data: params
        });
    },

    // 获取所有用工类型
    getAllEmploymentTypes: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/employment/public/types`,
            method: 'GET'
        });
    },

    // 获取所有工作类别
    getAllWorkCategories: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/employment/public/categories`,
            method: 'GET'
        });
    },

    // 获取所有薪资类型
    getAllSalaryTypes: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/employment/public/salary-types`,
            method: 'GET'
        });
    },

    // 根据关键词搜索用工信息
    searchEmployment: function(keyword) {
        return $.ajax({
            url: `${API_BASE_URL}/place/employment/public/search`,
            method: 'GET',
            data: { keyword: keyword }
        });
    },

    // 根据核心字段搜索用工信息
    coreSearchEmployment: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/place/employment/public/core-search`,
            method: 'GET',
            data: params
        });
    },

    // 获取用工信息详情
    getEmploymentDetail: function(employmentId) {
        return $.ajax({
            url: `${API_BASE_URL}/place/employment/public/${employmentId}`,
            method: 'GET'
        });
    }
};
// 全局变量
var placeInfoList = [];
var employmentInfoList = [];
var searchParams = {
    keyword: '',
    placeType: '',
    placeLevel: '',
    regionCode: '',
    industryDirection: '',
    operationMode: ''
};
var isPageReady = false;

// 显示加载状态
function showLoading() {
    document.getElementById('loadingState').style.display = 'flex';
    document.getElementById('placeGrid').style.display = 'none';
    document.getElementById('noDataTip').style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loadingState').style.display = 'none';
}

// 渲染场地信息列表
function renderPlaceInfoList(placeData) {
    var placeGrid = document.getElementById('placeGrid');
    var noDataTip = document.getElementById('noDataTip');

    hideLoading();

    if (!placeData || placeData.length === 0) {
        placeGrid.style.display = 'none';
        noDataTip.style.display = 'flex';
        return;
    }

    var html = '';
    placeData.forEach(function(place) {
        // 格式化租金显示
        var rentText = '面议';
        if (place.rentPriceMin && place.rentPriceMax) {
            rentText = '￥' + place.rentPriceMin + '-' + place.rentPriceMax + '/月/㎡';
        } else if (place.rentPriceMin) {
            rentText = '￥' + place.rentPriceMin + '+/月/㎡';
        }

        // 检查是否为推荐场地
        var cardClass = place.isFeatured == 1 ? 'place-card-new featured' : 'place-card-new';
        var featuredBadge = place.isFeatured == 1 ? '<div class="featured-badge">推荐</div>' : '';

        html += `
            <div class="${cardClass}" onclick="showPlaceDetails(${place.placeId})">
                ${featuredBadge}
                <!-- 卡片头部 -->
                <div class="card-header-new">
                    <h3 class="place-title-new" title="${place.placeName || ''}">${place.placeName || '--'}</h3>
                    <div class="place-rent-new">${rentText}</div>
                </div>

                <!-- 标签 -->
                <div class="place-meta-tags-new">
                    <span class="meta-tag-new type-tag-new">${place.placeType || '--'}</span>
                    <span class="meta-tag-new level-tag-new">${place.placeLevel || '--'}</span>
                    <span class="meta-tag-new region-tag-new">${place.regionName || '--'}</span>
                </div>

                <!-- 信息网格 -->
                <div class="info-grid-new">
                    <div class="info-item-new">
                        <div class="info-icon-new">📐</div>
                        <div class="info-content-new">
                            <div class="info-label-new">总面积</div>
                            <div class="info-value-new">${place.totalArea || '--'}㎡</div>
                        </div>
                    </div>
                    <div class="info-item-new">
                        <div class="info-icon-new">🏢</div>
                        <div class="info-content-new">
                            <div class="info-label-new">已入驻企业</div>
                            <div class="info-value-new">${place.companyCount || 0}家</div>
                        </div>
                    </div>
                    <div class="info-item-new">
                        <div class="info-icon-new">💺</div>
                        <div class="info-content-new">
                            <div class="info-label-new">可用工位</div>
                            <div class="info-value-new">${place.availablePositions || '--'}个</div>
                        </div>
                    </div>
                </div>

                <!-- 卡片底部 -->
                <div class="card-footer-new">
                    <div class="place-address-new" title="${place.address || ''}">${place.address || '--'}</div>
                    <div style="display: flex; gap: 15px; align-items: center;">
                        <span class="view-count-new">浏览 ${place.viewCount || 0} 次</span>
                        <button class="detail-btn-new" onclick="event.stopPropagation(); showPlaceDetails(${place.placeId})">查看详情</button>
                    </div>
                </div>
            </div>
        `;
    });

    placeGrid.innerHTML = html;
    placeGrid.style.display = 'block';
    noDataTip.style.display = 'none';

    console.log('场地信息列表渲染完成，共', placeData.length, '条数据');
}

// 加载场地信息列表
function loadPlaceInfoList() {
    showLoading();

    var params = {
        pageSize: 10,
        pageNum: 1
    };

    // 添加筛选条件
    if (searchParams.keyword) {
        params.placeName = searchParams.keyword;
    }
    if (searchParams.placeType) {
        params.placeType = searchParams.placeType;
    }
    if (searchParams.placeLevel) {
        params.placeLevel = searchParams.placeLevel;
    }
    if (searchParams.regionCode) {
        params.regionCode = searchParams.regionCode;
    }
    if (searchParams.industryDirection) {
        params.industryDirection = searchParams.industryDirection;
    }
    if (searchParams.operationMode) {
        params.operationMode = searchParams.operationMode;
    }

    console.log('场地信息请求参数:', params);

    placeAPI.getPlaceList(params)
        .done(function(response) {
            console.log('场地信息API响应:', response);

            if (response.code == 0 || response.code == 200) {
                var rows = response.rows || response.data || [];
                console.log('获取到的场地信息数据:', rows);

                placeInfoList = rows;
                renderPlaceInfoList(rows);
            } else {
                console.error('获取场地信息列表失败：', response.msg || response.message);
                hideLoading();
                renderPlaceInfoList([]);
            }
        })
        .fail(function(xhr, status, error) {
            console.error('场地信息请求失败:', error);
            hideLoading();
            renderPlaceInfoList([]);
        });
}

// 显示场地详情
function showPlaceDetails(placeId) {
    console.log('查看场地详情，ID:', placeId);
    // 这里可以跳转到场地详情页面或显示详情弹框
    window.open('./placeDetail.html?id=' + placeId);
}

// 显示用工信息加载状态
function showEmploymentLoading() {
    document.getElementById('employmentLoadingState').style.display = 'flex';
    document.getElementById('employmentGrid').style.display = 'none';
    document.getElementById('employmentNoDataTip').style.display = 'none';
}

// 隐藏用工信息加载状态
function hideEmploymentLoading() {
    document.getElementById('employmentLoadingState').style.display = 'none';
}

// 渲染用工信息列表
function renderEmploymentInfoList(employmentData) {
    var employmentGrid = document.getElementById('employmentGrid');
    var noDataTip = document.getElementById('employmentNoDataTip');

    hideEmploymentLoading();

    if (!employmentData || employmentData.length === 0) {
        employmentGrid.style.display = 'none';
        noDataTip.style.display = 'flex';
        return;
    }

    var html = '';
    employmentData.forEach(function(employment) {
        // 格式化薪资显示
        var salaryText = '面议';
        if (employment.salaryMin && employment.salaryMax) {
            var salaryUnit = getSalaryTypeDisplayName(employment.salaryType);
            salaryText = '￥' + employment.salaryMin + '-' + employment.salaryMax + '/' + salaryUnit;
        } else if (employment.salaryMin) {
            var salaryUnit = getSalaryTypeDisplayName(employment.salaryType);
            salaryText = '￥' + employment.salaryMin + '+/' + salaryUnit;
        }

        // 格式化发布时间
        var publishTimeText = '发布时间未知';
        if (employment.createTime) {
            var createDate = new Date(employment.createTime);
            var now = new Date();
            var diffTime = now - createDate;
            var diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 0) {
                publishTimeText = '今天发布';
            } else if (diffDays === 1) {
                publishTimeText = '昨天发布';
            } else if (diffDays < 7) {
                publishTimeText = diffDays + '天前发布';
            } else {
                publishTimeText = createDate.toLocaleDateString();
            }
        }

        // 检查是否为推荐用工信息
        var cardClass = employment.isFeatured == 1 ? 'employment-card-new featured' : 'employment-card-new';
        var featuredBadge = employment.isFeatured == 1 ? '<div class="featured-badge">推荐</div>' : '';

        html += `
            <div class="${cardClass}" onclick="showEmploymentDetails(${employment.employmentId})">
                ${featuredBadge}
                <!-- 卡片头部 -->
                <div class="card-header-new">
                    <h3 class="employment-title-new" title="${employment.title || ''}">${employment.title || '--'}</h3>
                    <div class="employment-salary-new">${salaryText}</div>
                </div>

                <!-- 标签 -->
                <div class="employment-meta-tags-new">
                    <span class="meta-tag-new employment-type-tag-new">${employment.employmentType || '--'}</span>
                    <span class="meta-tag-new employment-category-tag-new">${employment.workCategory || '--'}</span>
                    <span class="meta-tag-new employment-region-tag-new">${employment.regionName || '--'}</span>
                </div>

                <!-- 信息网格 -->
                <div class="info-grid-new">
                    <div class="info-item-new">
                        <div class="info-icon-new">🏢</div>
                        <div class="info-content-new">
                            <div class="info-label-new">公司</div>
                            <div class="info-value-new">${employment.companyName || '--'}</div>
                        </div>
                    </div>
                    <div class="info-item-new">
                        <div class="info-icon-new">👥</div>
                        <div class="info-content-new">
                            <div class="info-label-new">需要人数</div>
                            <div class="info-value-new">${employment.positionsNeeded || '--'}人</div>
                        </div>
                    </div>
                    <div class="info-item-new">
                        <div class="info-icon-new">📍</div>
                        <div class="info-content-new">
                            <div class="info-label-new">工作地点</div>
                            <div class="info-value-new">${employment.workLocation || '--'}</div>
                        </div>
                    </div>
                </div>

                <!-- 卡片底部 -->
                <div class="card-footer-new">
                    <div style="display: flex; gap: 15px;">
                        <span class="employment-company-new">${publishTimeText}</span>
                        <span class="view-count-new">浏览 ${employment.viewCount || 0} 次</span>
                    </div>
                    <div>
                        <button class="employment-btn-new" onclick="event.stopPropagation(); showEmploymentDetails(${employment.employmentId})">查看详情</button>
                    </div>
                </div>
            </div>
        `;
    });

    employmentGrid.innerHTML = html;
    employmentGrid.style.display = 'block';
    noDataTip.style.display = 'none';

    console.log('用工信息列表渲染完成，共', employmentData.length, '条数据');
}

// 获取薪资类型显示名称
function getSalaryTypeDisplayName(type) {
    var displayNames = {
        'hourly': '小时',
        'daily': '天',
        'monthly': '月',
        'piece': '件'
    };
    return displayNames[type] || type || '月';
}

// 加载用工信息列表
function loadEmploymentInfoList() {
    showEmploymentLoading();

    var params = {
        pageSize: 10,
        pageNum: 1
    };

    // 添加筛选条件
    if (viewModel.employmentTypeValue()) {
        params.employmentType = viewModel.employmentTypeValue();
    }
    if (viewModel.workCategoryValue()) {
        params.workCategory = viewModel.workCategoryValue();
    }
    if (viewModel.salaryTypeValue()) {
        params.salaryType = viewModel.salaryTypeValue();
    }
    if (viewModel.employmentRegionValue()) {
        params.regionCode = viewModel.employmentRegionValue();
    }

    console.log('用工信息请求参数:', params);

    employmentAPI.getEmploymentList(params)
        .done(function(response) {
            console.log('用工信息API响应:', response);

            if (response.code == 0 || response.code == 200) {
                var rows = response.rows || response.data || [];
                console.log('获取到的用工信息数据:', rows);

                employmentInfoList = rows;
                renderEmploymentInfoList(rows);

                // 更新knockout绑定的数据
                viewModel.employmentList(rows);
            } else {
                console.error('获取用工信息列表失败：', response.msg || response.message);
                hideEmploymentLoading();
                renderEmploymentInfoList([]);
            }
        })
        .fail(function(xhr, status, error) {
            console.error('用工信息请求失败:', error);
            hideEmploymentLoading();
            renderEmploymentInfoList([]);
        });
}

// 显示用工信息详情
function showEmploymentDetails(employmentId) {
    console.log('查看用工信息详情，ID:', employmentId);
    // 这里可以跳转到用工信息详情页面或显示详情弹框
    alert('查看用工信息详情，ID: ' + employmentId);
}

// 加载用工信息筛选选项
function loadEmploymentFilterOptions() {
    // 加载用工类型
    employmentAPI.getAllEmploymentTypes()
        .done(function(response) {
            if (response.code == 0 || response.code == 200) {
                var types = response.data || [];
                var typeOptions = [{ baseName: '全部', baseId: '' }];
                types.forEach(function(type) {
                    if (type) {
                        typeOptions.push({ baseName: type, baseId: type });
                    }
                });
                viewModel.employmentTypeList(typeOptions);
            }
        });

    // 加载工作类别
    employmentAPI.getAllWorkCategories()
        .done(function(response) {
            if (response.code == 0 || response.code == 200) {
                var categories = response.data || [];
                var categoryOptions = [{ baseName: '全部', baseId: '' }];
                categories.forEach(function(category) {
                    if (category) {
                        categoryOptions.push({ baseName: category, baseId: category });
                    }
                });
                viewModel.workCategoryList(categoryOptions);
            }
        });

    // 加载薪资类型
    employmentAPI.getAllSalaryTypes()
        .done(function(response) {
            if (response.code == 0 || response.code == 200) {
                var salaryTypes = response.data || [];
                var salaryOptions = [{ baseName: '全部', baseId: '' }];
                salaryTypes.forEach(function(type) {
                    if (type) {
                        salaryOptions.push({
                            baseName: getSalaryTypeDisplayName(type),
                            baseId: type
                        });
                    }
                });
                viewModel.salaryTypeList(salaryOptions);
            }
        });

    // 加载区域列表
    placeAPI.getAllRegions()
        .done(function(response) {
            if (response.code == 0 || response.code == 200) {
                var regions = response.data || [];
                var regionOptions = [{ baseName: '全部', baseId: '' }];
                regions.forEach(function(region) {
                    if (region && region.regionName) {
                        regionOptions.push({
                            baseName: region.regionName,
                            baseId: region.regionCode
                        });
                    }
                });
                viewModel.employmentRegionList(regionOptions);
            }
        });
}

// 用工信息筛选数据选择
viewModel.selectEmploymentData = function(type, event) {
    var text = $(event.target).text();
    var value = $(event.target).data('value') || $(event.target).text();

    if (text === '全部') {
        value = '';
    }

    switch(type) {
        case '0': // 用工类型
            viewModel.employmentTypeName(text);
            viewModel.employmentTypeValue(value);
            break;
        case '1': // 工作类别
            viewModel.workCategoryName(text);
            viewModel.workCategoryValue(value);
            break;
        case '2': // 薪资类型
            viewModel.salaryTypeName(text);
            viewModel.salaryTypeValue(value);
            break;
        case '3': // 区域
            viewModel.employmentRegionName(text);
            viewModel.employmentRegionValue(value);
            break;
    }

    // 重新加载数据
    loadEmploymentInfoList();

    // 隐藏下拉框
    $(event.target).closest('.selectContent').find('.selectList').hide();
};

// 更多用工信息列表
function moreEmploymentList() {
    console.log('查看更多用工信息');
    // 这里可以跳转到用工信息列表页面
    alert('跳转到用工信息列表页面');
}

var viewModel = {
    cdggList: ko.observableArray(),//公告列表
    cycdList: ko.observableArray(),
    cdxqCount: ko.observable(),//场地需求总数
    cdxqList: ko.observableArray(),//场地需求总数

    // 用工信息相关
    employmentList: ko.observableArray(),//用工信息列表
    employmentTypeList: ko.observableArray(),//用工类型列表
    workCategoryList: ko.observableArray(),//工作类别列表
    salaryTypeList: ko.observableArray(),//薪资类型列表
    employmentRegionList: ko.observableArray(),//区域列表

    // 用工信息筛选条件
    employmentTypeName: ko.observable('全部'),
    workCategoryName: ko.observable('全部'),
    salaryTypeName: ko.observable('全部'),
    employmentRegionName: ko.observable('全部'),

    // 用工信息筛选值
    employmentTypeValue: ko.observable(''),
    workCategoryValue: ko.observable(''),
    salaryTypeValue: ko.observable(''),
    employmentRegionValue: ko.observable(''),
    bagnnerList: ko.observableArray(),
    position: ko.observable(''),//场地位置：
        positionName: ko.observable('全部'),//场地位置：
        positionList: ko.observableArray(), //场地位置：

        type: ko.observable(''),//场地类型：
        typeName: ko.observable('全部'),//场地类型：
        typeList: ko.observableArray(),//场地类型：

        level: ko.observable(''),
        levelName: ko.observable('全部'),
        levelList: ko.observableArray(),
       
        area: ko.observable(''),//场地面积：
        areaName: ko.observable('全部'),//场地面积：
        areaList01: ko.observableArray(),

        money: ko.observable(''),//收费类型：
        moneyName: ko.observable('全部'),//收费类型：
        moneyList: ko.observableArray(),

        direction: ko.observable(''),//行业方向
        directionName: ko.observable('全部'),//行业方向
        directionList: ko.observableArray(),
        selectData: function (data, data2) { //筛选项
            changeSelectStyle(data,data2)
            switch (data) {
                case '0':
                    viewModel.position(data2.baseId)
                    localStorage.setItem('cdposition',data2.baseId)
                    viewModel.positionName(data2.baseName)
                    break;
                case '1':
                    viewModel.type(data2.baseId)
                    localStorage.setItem('cdtype',data2.baseId)
                    viewModel.typeName(data2.baseName)
                    initarea()
                    viewModel.area('')
                    viewModel.areaName('全部')
                    $('.module3').removeClass('on')
                    break;
            
                case '3':
                    viewModel.level(data2.baseId)
                    localStorage.setItem('cdlevel',data2.baseId)
                    viewModel.levelName(data2.baseName)
                    break;
                case '2':
                    viewModel.area(data2.baseId)
                    localStorage.setItem('cdarea',data2.baseId)
                    viewModel.areaName(data2.baseName)
                    break;
                case '4':
                    viewModel.direction(data2.baseId)
                    localStorage.setItem('cddirection',data2.baseId)
                    viewModel.directionName(data2.baseName)
                    break;
                case '5':
                    viewModel.money(data2.baseId)
                    localStorage.setItem('cdmoney',data2.baseId)
                    viewModel.moneyName(data2.baseName)
                    break;
            }
    
            getCycdList(1)
        },
        parkNoticeList:ko.observableArray(),
        qyzsList:ko.observableArray(),//园区招商
        yqzsInfo:function(data){
            window.open('./attractDet.html?id='+data.baseId)
        }
}
Object.assign(viewModel, viewModel1);
// 更多列表
function moreList(e){
    if(viewModel.cycdList().length>0){
        window.location.href='./placeList.html?index=1'
    }else{
        clearsx()
        window.location.href='./placeList.html'
    }
}
function changeSelectStyle(index,data){
    $('.selectModule').eq(index).addClass('on');

    if(!data.baseId){
        $('.selectModule').eq(index).removeClass('on');
    }
}
$('.selectList').mouseleave(function(){
    $(this).hide()
})
$('.selectModule').click(function(){
    if($(this).hasClass('module3')){
        if(!viewModel.type()){
            $.jBox.tip("请先选择场地类型！");
            return false
        }
    }
    $(this).siblings().show();
    for(var i = 0;i<$(this).parent().siblings().children().length;i++){
        if(i%2==1){
            $(this).parent().siblings().children().eq(i).hide();
        }
    }
})

// 初始化所有筛选选项数据
function initAllFilterOptions() {
    // 初始化区域列表
    placeAPI.getAllRegions().done(function(response) {
        if (response.code === 200) {
            var regions = response.data.map(function(item) {
                return {
                    baseName: item.regionName,
                    baseId: item.regionCode
                };
            });
            regions.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.positionList(regions);
        }
    });

    // 初始化场地类型
    placeAPI.getAllPlaceTypes().done(function(response) {
        if (response.code === 200) {
            var types = response.data.map(function(item) {
                return {
                    baseName: item,
                    baseId: item
                };
            });
            types.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.typeList(types);
        }
    });

    // 初始化场地等级
    placeAPI.getAllPlaceLevels().done(function(response) {
        if (response.code === 200) {
            var levels = response.data.map(function(item) {
                return {
                    baseName: item,
                    baseId: item
                };
            });
            levels.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.levelList(levels);
        }
    });

    // 初始化行业方向
    placeAPI.getAllIndustryDirections().done(function(response) {
        if (response.code === 200) {
            var directions = response.data.map(function(item) {
                return {
                    baseName: item,
                    baseId: item
                };
            });
            directions.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.directionList(directions);
        }
    });

    // 初始化运营模式（作为收费类型的替代）
    placeAPI.getAllOperationModes().done(function(response) {
        if (response.code === 200) {
            var modes = response.data.map(function(item) {
                return {
                    baseName: item,
                    baseId: item
                };
            });
            modes.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.moneyList(modes);
        }
    });
}

// 初始化场地面积选项（保持原有逻辑）
function initarea() {
    // 这里可以根据场地类型动态加载面积范围
    var areaOptions = [
        { baseName: '全部', baseId: '' },
        { baseName: '1000平方米以下', baseId: '0-1000' },
        { baseName: '1000-5000平方米', baseId: '1000-5000' },
        { baseName: '5000-10000平方米', baseId: '5000-10000' },
        { baseName: '10000平方米以上', baseId: '10000-999999' }
    ];
    viewModel.areaList01(areaOptions);
}
// 初始化 场地面积
function initarea() {
ajaxgetDataFull('api-app/v1/sysDictData/selectListByTypeid?typeId='+viewModel.type())
if (getData.code == 0) {
    var arr = getData.obj
    arr.unshift({
        baseName: '全部',
        baseId: ''
    })
    viewModel.areaList01(arr);
}
}
// banner管理
function initBanner(){
    var obj={
        pageNum:1,
        pageSize:1,
        bannerModule:2
    }
    ajaxgetDataFull('api-qingdao/v1/sysBannerManager/getFrontList',obj)
    if(getData.code==0){
        if(getData.obj.content.length>0){
            viewModel.bagnnerList(getData.obj.content[0].pictureFileList)
            viewModel.bagnnerList.unshift({fullPath:'./image/index_banner.png'})
        }
    }
}
// initBanner()
function linkPage(e){
    if(e==1){
        window.location.href='placeList.html'
    }else{
        window.location.href='demandList.html'
    }
}
// 场地公告
// getCdGg();
// function getCdGg() {
//     ajaxgetData('api-app/v1/qctParkNotice/top10', '', function (res) {
//         if (res.code == 0) {
//             res.obj.forEach(function (ele) {
//                 var date = ele.baseCreateTime
//                 ele.baseCreateTime = date.slice(0, 7);
//                 ele.date = date.slice(8, 10);
//                 ele.parkNoticeDetail = delHtmlTag(ele.parkNoticeDetail);
//             });
//             viewModel.cdggList(res.obj)
//             $(".slideTxtBox").slide({ mainCell: ".bd ul", autoPlay: true, effect: "left", interTime: 5000 });
//         }
//     })
// }
// 创业场地
getCycdList();
function getCycdList() {
    var params = {
        pageSize: 6,
        pageNum: 1
    };

    // 添加筛选条件
    if (viewModel.level()) {
        params.placeLevel = viewModel.level();
    }
    if (viewModel.position()) {
        params.regionCode = viewModel.position();
    }
    if (viewModel.type()) {
        params.placeType = viewModel.type();
    }
    if (viewModel.direction()) {
        params.industryDirection = viewModel.direction();
    }
    if (viewModel.money()) {
        params.operationMode = viewModel.money();
    }

    placeAPI.getPlaceList(params).done(function(response) {
        if (response.code === 200) {
            var places = response.rows || [];
            if (places.length > 0) {
                $(".nodataPicCycd").hide(); // 暂无数据-隐藏
            } else {
                $(".nodataPicCycd").show(); // 暂无数据-显示
            }

            // 数据格式转换，适配现有模板
            places.forEach(function(place) {
                place.parkName = place.placeName;
                place.parkAddress = place.address;
                place.parkLevel = place.placeLevel;
                place.parkType = place.placeType;
                place.parkArea = place.placeArea;
                place.usableArea = place.usableArea;
                place.companyCount = place.companyCount;
                place.availablePositions = place.availablePositions;
                place.occupiedPositions = place.occupiedPositions;
                place.rentPriceMin = place.rentPriceMin;
                place.rentPriceMax = place.rentPriceMax;
                place.industryDirection = place.industryDirection;
                place.operationMode = place.operationMode;
                place.contactPerson = place.contactPerson;
                place.contactPhone = place.contactPhone;
                place.description = place.description;
                place.imageUrl = place.imageUrl || './image/place_default.jpg';
            });

            viewModel.cycdList(places);
        }
    }).fail(function() {
        $(".nodataPicCycd").show();
        viewModel.cycdList([]);
    });
}
// 园区动态
getNoticeList();
function getNoticeList() {
    var obj = {
        pageSize: 2,
        pageNum: 1
    };
    ajaxgetData('api-qingdao/v1/qctParkInformation/front/getParkInformationList', obj, function (data) {
        viewModel.parkNoticeList(data.obj.content);
    });
}
// 初始化统计数据
initnum()
function initnum(){
    placeAPI.getPlaceStatistics().done(function(response) {
        if (response.code === 200) {
            var stats = response.data;

            // 场地总数
            $("#onerun01").numberAnimate({
                num: stats.total_count || 0,
                speed: 2000
            });

            // 总面积（平方米）
            $("#onerun02").numberAnimate({
                num: Math.round(stats.total_place_area || 0),
                speed: 2000
            });

            // 可使用面积（平方米）
            $("#onerun03").numberAnimate({
                num: Math.round(stats.total_usable_area || 0),
                speed: 2000
            });

            // 入驻企业总数
            $("#onerun04").numberAnimate({
                num: stats.total_company_count || 0,
                speed: 2000
            });
        }
    }).fail(function() {
        // 失败时显示默认值
        $("#onerun01").numberAnimate({ num: 0, speed: 2000 });
        $("#onerun02").numberAnimate({ num: 0, speed: 2000 });
        $("#onerun03").numberAnimate({ num: 0, speed: 2000 });
        $("#onerun04").numberAnimate({ num: 0, speed: 2000 });
    });
}

// 场地需求
getCdxqList();
function getCdxqList() {
    var obj = {
        pageSize: 4,
        pageNum: 1,
        publisher:0
    };
    ajaxgetData('api-qingdao/v1/qctParkRequire/frontend', obj, function (data) {
        // data.obj.content=[]
        if (data.obj.content.length > 0) {
            $(".nodataPicCdxq").hide();//暂无数据-隐藏    
        } else {
            $(".nodataPicCdxq").show();//暂无数据-显示
        }
        viewModel.cdxqCount(data.obj.totalCount);
        data.obj.content.forEach(function (ele) {
            ele.demandIntroduct = delHtmlTag(ele.demandIntroduct);
        });
        viewModel.cdxqList(data.obj.content);
    });
}
// 园区招商需求
getQyzsList();
function getQyzsList() {
    var obj = {
        pageSize: 4,
        pageNum: 1,
        publisher:1
    };
    ajaxgetData('api-qingdao/v1/qctParkRequire/frontend', obj, function (data) {
        if (data.obj.content.length > 0) {
            $(".nodataPicQyzs").hide();//暂无数据-隐藏    
        } else {
            $(".nodataPicQyzs").show();//暂无数据-显示
        }
        viewModel.qyzsList(data.obj.content);
    });
}
// 申请入驻
function sqrzFun() {
    if (!checkLogin()) {
        $.jBox.tip("请先登录");
        // setTimeout(function () {
        //     //跳转到登录界面
        //     //sessionStorage.setItem('loginUrl', '../place/index.html');
        //     window.location.href = 'https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDCHUANGYEYPT&userType=2';
        // }, 500);
    } else {
        if (viewModel.mine().userType ==0) {//个人类型账号
            $.jBox.tip("个人账号无法认证为场地服务商，请通过企业账号进行申请入驻操作！");
        }else{
            if (viewModel.mine().fieldService == '0' || viewModel.mine().fieldService == '2' || viewModel.mine().fieldService == '3') {//未认证为场地服务商
                window.open("../member/authSpace.html?type=3");//跳转认证页面
            } else if (viewModel.mine().fieldService == '1') {//已经认证为场地服务商
                $.jBox.tip("您已认证为场地服务商");
            }
        }
        
    }
}
// 发布需求
function fbxqNow() {
    if (!checkLogin()) {
        $.jBox.tip("请先登录");
        // setTimeout(function () {
        //     //跳转到登录界面
        //     //sessionStorage.setItem('loginUrl', '../place/index.html');
        //     window.location.href = 'https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDCHUANGYEYPT&userType=2';
        // }, 500);
    } else {
        if (viewModel.mine().fieldService == '1') {
            $.jBox.tip("场地服务商无法发布场地需求！")
            return false;
        }
        window.open("../member/parkDemandList.html");//跳转我发布的场地需求页面
    }
}
ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
$(".bannerBox").slide({
    titCell:".hd ul",
    mainCell:".bannerSlide",
    autoPlay:true,
    effect:"fold",
    interTime:5000,
    autoPage:true
});
$(".yqdtOut").slide({
    mainCell:"ul",
    autoPlay:true,
    effect:"top",
    interTime:5000,
    autoPage:true,
    vis:2,
    scroll:2
});

// 初始化新的场地信息页面
function initPlaceInfoPage() {
    console.log('开始初始化场地信息页面...');

    // 标记页面已准备好
    isPageReady = true;

    console.log('场地信息页面初始化完成，开始加载数据...');

    // 加载场地信息列表
    loadPlaceInfoList();

    // 加载统计数据
    loadPlaceStatistics();

    // 加载用工信息相关数据
    loadEmploymentFilterOptions();
    loadEmploymentInfoList();
}

// 加载场地统计数据
function loadPlaceStatistics() {
    placeAPI.getPlaceStatistics()
        .done(function(response) {
            console.log('场地统计数据响应:', response);

            if (response.code == 0 || response.code == 200) {
                var statistics = response.data || {};

                // 更新统计数字
                if (statistics.totalPlaces) {
                    $('#onerun01').text(statistics.totalPlaces);
                }
                if (statistics.totalArea) {
                    $('#onerun02').text(statistics.totalArea);
                }
                if (statistics.availableArea) {
                    $('#onerun03').text(statistics.availableArea);
                }
                if (statistics.totalCompanies) {
                    $('#onerun04').text(statistics.totalCompanies);
                }
            }
        })
        .fail(function(xhr, status, error) {
            console.error('获取场地统计数据失败:', error);
        });
}

// 筛选场地信息
function filterPlaces() {
    // 这里可以添加筛选逻辑
    console.log('筛选场地信息...');
    loadPlaceInfoList();
}

// 搜索场地信息
function searchPlaces() {
    var keyword = document.getElementById('placeSearchInput');
    if (keyword) {
        searchParams.keyword = keyword.value.trim();
        console.log('开始搜索，关键词:', searchParams.keyword);
        loadPlaceInfoList();
    }
}

// 刷新场地信息数据
function refreshPlaceData() {
    console.log('刷新场地信息数据...');

    // 清空筛选条件
    searchParams = {
        keyword: '',
        placeType: '',
        placeLevel: '',
        regionCode: '',
        industryDirection: '',
        operationMode: ''
    };

    // 重新加载数据
    loadPlaceInfoList();
}

// 初始化所有数据
$(document).ready(function() {
    initAllFilterOptions(); // 初始化筛选选项
    initarea(); // 初始化面积选项
    initnum(); // 初始化统计数据
    getCycdList(); // 获取场地列表（原有的knockout绑定）

    // 初始化新的场地信息页面
    initPlaceInfoPage();
});
